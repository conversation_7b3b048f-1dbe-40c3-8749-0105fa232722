import React, { useState, useEffect } from 'react';
import styled, { keyframes } from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { FaChevronLeft, FaChevronRight, FaHeart } from 'react-icons/fa';
// Placeholder images for memories
// To use your own images, place them in src/assets/images/ and update these imports
const getPlaceholderImage = (index: number) => 
  `https://placehold.co/600x400/${['ffb6c1', 'ffd1dc', 'e0bbff', 'a0e7e5', 'b4f8c8'][index % 5]}/333333?text=Memory+${index + 1}`;

interface Memory {
  id: number;
  title: string;
  description: string;
  imageUrl?: string; // Optional, can use placeholder if not provided
}

interface MemoryGalleryProps {
  name: string;
  onContinue: () => void;
}

// Enhanced interface for memories
interface Memory {
  id: number;
  title: string;
  description: string;
  date: string;
  emotion: string; // To represent the emotion associated with this memory
  imageUrl?: string; // Optional, can use placeholder if not provided
}

// Memories reflecting our online relationship with enhanced content
const sampleMemories: Memory[] = [
  {
    id: 1,
    title: "Our First Video Call",
    description: "Remember our first video call? We talked for hours, laughing and sharing stories. The screen couldn't contain our connection. Your smile lit up my world even through pixels and distance.",
    date: "October 15, 2024",
    emotion: "Joy",
    imageUrl: getPlaceholderImage(0)
  },
  {
    id: 2,
    title: "That Unforgettable Prank",
    description: "That extraordinary night when you pranked me still makes me laugh. Your playful spirit and the way your eyes sparkled with mischief is something I'll never forget. Some connections transcend distance.",
    date: "October 22, 2024",
    emotion: "Laughter",
    imageUrl: getPlaceholderImage(1)
  },
  {
    id: 3,
    title: "Midnight Conversations",
    description: "Those nights we stayed up texting until the early hours, sharing our dreams and deepest thoughts. Distance faded with every word. Time zones couldn't separate our hearts as we built our own world message by message.",
    date: "December 10, 2024",
    emotion: "Connection",
    imageUrl: getPlaceholderImage(2)
  },
  {
    id: 4,
    title: "Missing Your Presence",
    description: "What magic have you woven? I can't get enough of your presence in my life. Even when we're apart, I feel you close to my heart. Your essence has become part of my daily thoughts and dreams.",
    date: "Janaury 18, 2024",
    emotion: "Longing",
    imageUrl: getPlaceholderImage(3)
  },
  {
    id: 5,
    title: "Dreams We've Shared",
    description: "The dreams we've envisioned together paint such a beautiful future. Every conversation builds another room in the castle of our shared imagination. Don't you want to make these dreams into reality? Our connection deserves to flourish beyond screens.",
    date: "October 25, 2024",
    emotion: "Hope",
    imageUrl: getPlaceholderImage(4)
  }
];

const MemoryGallery: React.FC<MemoryGalleryProps> = ({ name, onContinue }) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [direction, setDirection] = useState(0);
  const [isComplete, setIsComplete] = useState(false);

  const currentMemory = sampleMemories[currentIndex];

  const nextMemory = () => {
    setDirection(1);
    setCurrentIndex((prevIndex) =>
      prevIndex === sampleMemories.length - 1 ? 0 : prevIndex + 1
    );
  };

  const prevMemory = () => {
    setDirection(-1);
    setCurrentIndex((prevIndex) =>
      prevIndex === 0 ? sampleMemories.length - 1 : prevIndex - 1
    );
  };

  const handleComplete = () => {
    setIsComplete(true);
    setTimeout(() => {
      onContinue();
    }, 1000);
  };

  // Auto-advance every 8 seconds
  useEffect(() => {
    const timer = setInterval(() => {
      nextMemory();
    }, 8000);

    return () => clearInterval(timer);
  }, []);

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      x: direction > 0 ? -1000 : 1000,
      opacity: 0
    })
  };

  return (
    <Container>
      {/* Decorative floating elements */}
      <FloatingElements>
        {Array.from({ length: 15 }).map((_, index) => (
          <FloatingElement
            key={index}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDuration: `${Math.random() * 15 + 10}s`,
              animationDelay: `${Math.random() * 5}s`,
              fontSize: `${Math.random() * 20 + 15}px`,
            }}
          >
            {['❤️', '✨', '💫', '💕', '🌟'][Math.floor(Math.random() * 5)]}
          </FloatingElement>
        ))}
      </FloatingElements>

      <Header
        initial={{ opacity: 0, y: -50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 1 }}
      >
        <Title>
          <HeartDecoration>❤️</HeartDecoration>
          Our Digital Journey Together, {name}
          <HeartDecoration>❤️</HeartDecoration>
        </Title>
        <Subtitle>Memories that transcend distance</Subtitle>
      </Header>

      <GalleryContainer>
        <AnimatePresence custom={direction} mode="wait">
          <MemoryCard
            key={currentMemory.id}
            custom={direction}
            variants={slideVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            <ImageContainer>
              {currentMemory.imageUrl ? (
                <MemoryImage src={currentMemory.imageUrl} alt={currentMemory.title} />
              ) : (
                <PlaceholderImage>
                  <HeartIcon>
                    <FaHeart size={40} />
                  </HeartIcon>
                </PlaceholderImage>
              )}
              <EmotionTag>{currentMemory.emotion}</EmotionTag>
            </ImageContainer>

            <MemoryContent>
              <MemoryTitle>{currentMemory.title}</MemoryTitle>
              <MemoryDate>{currentMemory.date}</MemoryDate>
              <MemoryDescription>{currentMemory.description}</MemoryDescription>
            </MemoryContent>

            <MemoryCounter>
              {currentIndex + 1} / {sampleMemories.length}
            </MemoryCounter>
          </MemoryCard>
        </AnimatePresence>

        <NavButton left onClick={prevMemory}>
          <FaChevronLeft />
        </NavButton>

        <NavButton right onClick={nextMemory}>
          <FaChevronRight />
        </NavButton>
      </GalleryContainer>

      <ContinueButton
        onClick={handleComplete}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        animate={isComplete ? { scale: [1, 1.2, 0] } : {}}
        transition={isComplete ? { duration: 0.8 } : {}}
      >
        Continue to Your Message
      </ContinueButton>

      <ProgressBar>
        {sampleMemories.map((_, index) => (
          <ProgressDot
            key={index}
            active={index === currentIndex}
            onClick={() => {
              setDirection(index > currentIndex ? 1 : -1);
              setCurrentIndex(index);
            }}
          />
        ))}
      </ProgressBar>
    </Container>
  );
};

const Container = styled.div`
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: 400% 400%;
  animation: gradientAnimation 15s ease infinite;
  padding: 2rem;
  overflow: hidden;

  @keyframes gradientAnimation {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }
`;

const FloatingElements = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 1;
`;

const floatAnimation = keyframes`
  0% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-100px) rotate(180deg);
    opacity: 0.8;
  }
  100% {
    transform: translateY(-200px) rotate(360deg);
    opacity: 0.3;
  }
`;

const FloatingElement = styled.div`
  position: absolute;
  animation: ${floatAnimation} linear infinite;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
`;

const Header = styled(motion.div)`
  margin-bottom: 2rem;
  text-align: center;
`;

const Title = styled.h2`
  font-size: 2.5rem;
  color: #fff;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  font-family: 'Dancing Script', cursive, sans-serif;
`;

const HeartDecoration = styled.span`
  display: inline-block;
  animation: pulse 1.5s infinite alternate;
  font-size: 1.8rem;

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(1.3);
    }
  }
`;

const Subtitle = styled.p`
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
  font-style: italic;
  margin-top: 0.5rem;
`;

const GalleryContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 800px;
  height: 400px;
  margin: 0 auto;
  overflow: hidden;
`;

const MemoryCard = styled(motion.div)`
  position: absolute;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2), 0 0 20px rgba(255, 154, 158, 0.3);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transform-style: preserve-3d;
  perspective: 1000px;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0) 50%,
      rgba(255, 255, 255, 0.1) 100%
    );
    z-index: 1;
    pointer-events: none;
  }
`;

const ImageContainer = styled.div`
  width: 100%;
  height: 60%;
  overflow: hidden;
  position: relative;

  &:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 30%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
    pointer-events: none;
  }
`;

const MemoryImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;

  &:hover {
    transform: scale(1.05);
  }
`;

const PlaceholderImage = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
`;

const HeartIcon = styled.div`
  color: rgba(255, 255, 255, 0.8);
  animation: pulse 2s infinite alternate;

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 0.5;
    }
    100% {
      transform: scale(1.2);
      opacity: 0.8;
    }
  }
`;

const EmotionTag = styled.div`
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: linear-gradient(135deg, rgba(255, 154, 158, 0.8) 0%, rgba(255, 106, 136, 0.8) 100%);
  color: white;
  padding: 0.4rem 1rem;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(5px);
  z-index: 5;
`;

const MemoryContent = styled.div`
  padding: 1.5rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
`;

const MemoryTitle = styled.h3`
  font-size: 1.8rem;
  color: #fff;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  font-family: 'Dancing Script', cursive, sans-serif;
`;

const MemoryDate = styled.div`
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1rem;
  font-style: italic;
  display: flex;
  align-items: center;

  &:before {
    content: '📅';
    margin-right: 0.5rem;
  }
`;

const MemoryDescription = styled.p`
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  position: relative;
  padding-left: 1rem;
  border-left: 3px solid rgba(255, 154, 158, 0.8);
`;

const MemoryCounter = styled.div`
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.7);
  background: rgba(0, 0, 0, 0.2);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
`;

const NavButton = styled.button<{ left?: boolean; right?: boolean }>`
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  ${props => props.left ? 'left: 1rem;' : ''}
  ${props => props.right ? 'right: 1rem;' : ''}
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 154, 158, 0.8) 0%, rgba(255, 106, 136, 0.8) 100%);
  backdrop-filter: blur(5px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 1.2rem;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);

  &:hover {
    background: linear-gradient(135deg, rgba(255, 154, 158, 0.9) 0%, rgba(255, 106, 136, 0.9) 100%);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
  }

  &:active {
    transform: translateY(-50%) scale(0.95);
  }
`;

const ContinueButton = styled(motion.button)`
  margin-top: 2rem;
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #ff9a9e 0%, #ff6a88 100%);
  border: none;
  border-radius: 50px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
`;

const ProgressBar = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 1.5rem;
  gap: 0.5rem;
`;

const ProgressDot = styled.div<{ active: boolean }>`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: ${props => props.active ? 'white' : 'rgba(255, 255, 255, 0.3)'};
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: scale(1.2);
  }
`;

export default MemoryGallery;
