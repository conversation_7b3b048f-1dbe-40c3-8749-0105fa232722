import React, { useState, useEffect, useRef } from 'react';
import styled, { keyframes } from 'styled-components';
import { motion } from 'framer-motion';
import gsap from 'gsap';
// Video functionality temporarily disabled to fix startup issues
// const backgroundVideo = '/videos/background.mp4';

// Import all the girlfriend's photos
import dashai from '../../assets/images/dashai.jpg';
import dibs from '../../assets/images/dibs.jpg';
import dibya from '../../assets/images/dibya.jpg';
import divya from '../../assets/images/divya.jpg';
import inocent from '../../assets/images/inocent.jpg';
import love from '../../assets/images/love.jpg';
import pd from '../../assets/images/Pd.jpg';
import queen from '../../assets/images/queen.jpg';
import smile from '../../assets/images/smile.jpg';

// Array of all photos for the slideshow
const photos = [
  { src: dashai, alt: '<PERSON><PERSON>' },
  { src: dibs, alt: 'Dibs' },
  { src: dibya, alt: 'Dibya' },
  { src: divya, alt: 'Divya' },
  { src: inocent, alt: 'Innocent' },
  { src: love, alt: 'Love' },
  { src: pd, alt: 'PD' },
  { src: queen, alt: 'Queen' },
  { src: smile, alt: 'Smile' }
];

const PhotoVideoBackground: React.FC = () => {
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);
  const [transitionType, setTransitionType] = useState<'fade' | 'zoom' | 'slide'>('fade');
  // We only need to track if we're showing video or photos
  const [showVideo, setShowVideo] = useState(false);
  const [videoLoaded, setVideoLoaded] = useState(false);

  // Log state changes for debugging
  useEffect(() => {
    console.log("showVideo state changed to:", showVideo);
  }, [showVideo]);

  const containerRef = useRef<HTMLDivElement>(null);
  const photoRefs = useRef<(HTMLDivElement | null)[]>([]);
  const videoRef = useRef<HTMLVideoElement>(null);

  // Initialize photo refs
  useEffect(() => {
    photoRefs.current = photoRefs.current.slice(0, photos.length);
  }, []);

  // Handle video loading and errors
  useEffect(() => {
    const handleVideoLoaded = () => {
      console.log("Video loaded successfully");
      setVideoLoaded(true);
    };

    const handleVideoError = () => {
      setVideoLoaded(false);
      console.error("Error loading video, video not available");
    };

    const video = videoRef.current;
    if (video) {
      video.addEventListener('loadeddata', handleVideoLoaded);
      video.addEventListener('error', handleVideoError);

      return () => {
        video.removeEventListener('loadeddata', handleVideoLoaded);
        video.removeEventListener('error', handleVideoError);
      };
    }
  }, []);

  // Toggle function for switching between video and photo backgrounds
  function toggleBackground() {
    // Only allow toggle if video is available
    if (!videoLoaded && !showVideo) {
      console.log("Video not loaded, cannot switch to video mode");
      return;
    }

    // Create a local variable to store the new state
    const newState = !showVideo;
    console.log("Toggle button clicked. Current showVideo state:", showVideo);
    console.log("Setting showVideo to:", newState);

    // Update the state
    setShowVideo(newState);

    // Force a re-render by updating the DOM directly
    if (newState) {
      // Show video, hide photos
      if (videoRef.current) {
        videoRef.current.style.display = 'block';
        videoRef.current.play();
      }
      photoRefs.current.forEach(photo => {
        if (photo) photo.style.display = 'none';
      });
    } else {
      // Show photos, hide video
      if (videoRef.current) {
        videoRef.current.style.display = 'none';
        videoRef.current.pause();
      }
      photoRefs.current.forEach((photo, index) => {
        if (photo) {
          photo.style.display = 'block';
          photo.style.opacity = index === currentPhotoIndex ? '1' : '0';
        }
      });
    }
  }

  // Advanced photo transition effect
  useEffect(() => {
    const transitionTypes: ('fade' | 'zoom' | 'slide')[] = ['fade', 'zoom', 'slide'];
    const interval = setInterval(() => {
      // Choose a random transition type for variety
      const newTransitionType = transitionTypes[Math.floor(Math.random() * transitionTypes.length)];
      setTransitionType(newTransitionType);

      // Animate out current photo
      if (photoRefs.current[currentPhotoIndex]) {
        const currentPhoto = photoRefs.current[currentPhotoIndex];
        gsap.to(currentPhoto, {
          opacity: 0,
          scale: newTransitionType === 'zoom' ? 1.2 : 1,
          x: newTransitionType === 'slide' ? -100 : 0,
          duration: 1.5,
          ease: "power2.inOut"
        });
      }

      // Set next photo index
      const nextIndex = (currentPhotoIndex + 1) % photos.length;

      // Prepare and animate in next photo
      if (photoRefs.current[nextIndex]) {
        const nextPhoto = photoRefs.current[nextIndex];
        gsap.fromTo(nextPhoto,
          {
            opacity: 0,
            scale: newTransitionType === 'zoom' ? 1.2 : 1,
            x: newTransitionType === 'slide' ? 100 : 0
          },
          {
            opacity: 1,
            scale: 1,
            x: 0,
            duration: 1.5,
            delay: 0.5,
            ease: "power2.inOut"
          }
        );
      }

      // Update current photo index
      setTimeout(() => {
        setCurrentPhotoIndex(nextIndex);
      }, 500);

    }, 8000); // 8 seconds between photo changes

    return () => clearInterval(interval);
  }, [currentPhotoIndex]);

  // Create floating elements with different shapes and colors
  const renderFloatingElements = () => {
    const elements = [];
    const shapes = ['❤️', '✨', '🌟', '💫', '💕'];

    for (let i = 0; i < 30; i++) {
      const shape = shapes[Math.floor(Math.random() * shapes.length)];
      const size = Math.random() * 20 + 10;
      const duration = Math.random() * 15 + 10;
      const delay = Math.random() * 5;
      const left = Math.random() * 100;
      const top = Math.random() * 100;
      const opacity = Math.random() * 0.5 + 0.1;

      elements.push(
        <FloatingElement
          key={i}
          style={{
            left: `${left}%`,
            top: `${top}%`,
            animationDuration: `${duration}s`,
            animationDelay: `${delay}s`,
            fontSize: `${size}px`,
            opacity: opacity
          }}
        >
          {shape}
        </FloatingElement>
      );
    }

    return elements;
  };

  return (
    <Container ref={containerRef}>
      {/* Video Background - Temporarily disabled */}
      {false && (
        <VideoBackground id="video-background">
          <video
            ref={videoRef}
            autoPlay={showVideo}
            loop
            muted
            playsInline
            style={{ display: showVideo ? 'block' : 'none' }}
            onError={() => setVideoLoaded(false)}
          >
            {/* Video source temporarily disabled */}
            <source src="/videos/background.mp4" type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        </VideoBackground>
      )}

      {/* Photo layers - always render but control visibility with CSS */}
      {photos.map((photo, index) => (
        <PhotoContainer
          key={index}
          id={`photo-${index}`}
          ref={(el: HTMLDivElement | null) => {
            photoRefs.current[index] = el;
            return undefined;
          }}
          style={{
            backgroundImage: `url(${photo.src})`,
            opacity: !showVideo && index === currentPhotoIndex ? 1 : 0,
            zIndex: index === currentPhotoIndex ? 2 : 1,
            display: showVideo ? 'none' : 'block'
          }}
          className={`transition-${transitionType} ${!showVideo ? 'active' : ''}`}
          data-index={index}
          data-current={index === currentPhotoIndex ? 'true' : 'false'}
        />
      ))}

      {/* Floating elements overlay */}
      <FloatingElementsOverlay>
        {renderFloatingElements()}
      </FloatingElementsOverlay>

      {/* Light rays effect */}
      <LightRaysEffect />

      {/* Overlay with video-like effect */}
      <VideoEffect />

      {/* Dark overlay to ensure text readability */}
      <DarkOverlay />

      {/* Message overlay */}
      <MessageOverlay>
        <MessageContainer>
          <TopMessage>Happy Birthday!</TopMessage>
          <Message>जन्मदिनको धेरै धेरै शुभकामना Dalli! ❤️</Message>
        </MessageContainer>
      </MessageOverlay>

      {/* Toggle button for switching between video and photos */}
      <ToggleButton
        onClick={toggleBackground}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        id="toggle-background-button"
        style={{ opacity: videoLoaded || showVideo ? 1 : 0.5 }}
      >
{'📷 Photo Gallery Mode'}
      </ToggleButton>

      {/* Video loading status indicator */}
      {!videoLoaded && (
        <LoadingIndicator>
          🎬 Video Loading...
        </LoadingIndicator>
      )}

      {/* Debug info - can be removed in production */}
      <DebugInfo>
        Current mode: {showVideo ? 'Video' : 'Photos'}<br />
        Current photo: {currentPhotoIndex + 1} / {photos.length}<br />
        Video loaded: {videoLoaded ? '✅ Yes' : '❌ No'}
      </DebugInfo>
    </Container>
  );
};

const floatAnimation = keyframes`
  0% {
    transform: translateY(0) rotate(0deg);
  }
  50% {
    transform: translateY(-100px) rotate(20deg);
  }
  100% {
    transform: translateY(-200px) rotate(40deg);
  }
`;

const scanlineEffect = keyframes`
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 0 100%;
  }
`;

const lightRayAnimation = keyframes`
  0% {
    transform: rotate(0deg) translateY(0);
    opacity: 0.3;
  }
  50% {
    transform: rotate(180deg) translateY(100px);
    opacity: 0.6;
  }
  100% {
    transform: rotate(360deg) translateY(0);
    opacity: 0.3;
  }
`;

const pulse = keyframes`
  0% {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 255, 255, 0.3);
  }
  100% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.6), 0 0 40px rgba(255, 255, 255, 0.5);
  }
`;

const Container = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
`;

const VideoBackground = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 2;

  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: absolute;
    top: 0;
    left: 0;
  }
`;

const PhotoContainer = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  transition: opacity 1s ease-in-out, transform 1.5s ease-in-out;
  will-change: opacity, transform;

  &.transition-fade {
    transition: opacity 1.5s ease-in-out;
  }

  &.transition-zoom {
    transition: opacity 1.5s ease-in-out, transform 1.5s ease-in-out;
  }

  &.transition-slide {
    transition: opacity 1.5s ease-in-out, transform 1.5s ease-in-out;
  }
`;

const FloatingElementsOverlay = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 5;
  pointer-events: none;
`;

const FloatingElement = styled.div`
  position: absolute;
  animation: ${floatAnimation} linear infinite;
  pointer-events: none;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));
`;

const LightRaysEffect = styled.div`
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  background: radial-gradient(
    ellipse at center,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  z-index: 3;
  pointer-events: none;
  animation: ${lightRayAnimation} 20s infinite linear;
  opacity: 0.3;
`;

const VideoEffect = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 4;
  background: linear-gradient(
    transparent 50%,
    rgba(0, 0, 0, 0.05) 50%
  );
  background-size: 100% 4px;
  animation: ${scanlineEffect} 10s linear infinite;
  pointer-events: none;
  opacity: 0.3;
`;

const DarkOverlay = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 6;
  background: rgba(0, 0, 0, 0.4);
  pointer-events: none;
`;

const MessageOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: auto; /* Changed from 100% to auto */
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding-top: 0;
  z-index: 2; /* Reduced z-index to be below the main content */
  pointer-events: none;
  max-height: 25vh; /* Limit height to 25% of viewport height */
`;

const MessageContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  padding: 0.5rem 1.5rem;
  border-radius: 0 0 15px 15px; /* Rounded only at the bottom */
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  border-top: 2px solid rgba(255, 105, 180, 0.5); /* Decorative top border */
  max-width: 60%;
  width: auto;

  @media (max-height: 800px) {
    padding: 0.3rem 1rem;
    max-width: 50%;
  }

  @media (max-height: 600px) {
    padding: 0.2rem 0.8rem;
    max-width: 40%;
  }
`;

const TopMessage = styled.div`
  font-family: 'Montserrat', sans-serif;
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 900;
  color: white;
  text-shadow: 0 0 15px rgba(0, 0, 0, 0.8), 0 0 30px rgba(255, 105, 180, 0.9), 0 0 45px rgba(255, 215, 0, 0.6);
  margin-bottom: 0.5rem;
  letter-spacing: 3px;
  text-transform: uppercase;
  background: linear-gradient(135deg, #ffffff 0%, #ffd700 50%, #ff69b4 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.5));
  animation: birthdayPulse 2s ease-in-out infinite alternate;

  @keyframes birthdayPulse {
    0% {
      transform: scale(1);
      text-shadow: 0 0 15px rgba(0, 0, 0, 0.8), 0 0 30px rgba(255, 105, 180, 0.9), 0 0 45px rgba(255, 215, 0, 0.6);
    }
    100% {
      transform: scale(1.05);
      text-shadow: 0 0 20px rgba(0, 0, 0, 0.9), 0 0 40px rgba(255, 105, 180, 1), 0 0 60px rgba(255, 215, 0, 0.8);
    }
  }

  @media (max-height: 800px) {
    font-size: clamp(2rem, 5vw, 3rem);
    margin-bottom: 0.3rem;
    letter-spacing: 2px;
  }

  @media (max-height: 600px) {
    font-size: clamp(1.5rem, 4vw, 2.5rem);
    margin-bottom: 0.2rem;
    letter-spacing: 1px;
  }
`;

const Message = styled.div`
  font-family: 'Dancing Script', cursive, sans-serif;
  font-size: clamp(2.5rem, 5vw, 3.5rem);
  font-weight: 700;
  color: white;
  text-shadow: 0 0 12px rgba(0, 0, 0, 0.8), 0 0 24px rgba(255, 105, 180, 0.8), 0 0 36px rgba(255, 215, 0, 0.5);
  padding: 0.5rem 1.2rem;
  border-radius: 15px;
  letter-spacing: 1px;
  background: linear-gradient(135deg, rgba(255, 105, 180, 0.2), rgba(255, 215, 0, 0.1));
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  animation: nepaliGlow 3s ease-in-out infinite alternate;

  @keyframes nepaliGlow {
    0% {
      transform: scale(1);
      text-shadow: 0 0 12px rgba(0, 0, 0, 0.8), 0 0 24px rgba(255, 105, 180, 0.8), 0 0 36px rgba(255, 215, 0, 0.5);
      background: linear-gradient(135deg, rgba(255, 105, 180, 0.2), rgba(255, 215, 0, 0.1));
    }
    100% {
      transform: scale(1.05);
      text-shadow: 0 0 16px rgba(0, 0, 0, 0.9), 0 0 32px rgba(255, 105, 180, 1), 0 0 48px rgba(255, 215, 0, 0.7);
      background: linear-gradient(135deg, rgba(255, 105, 180, 0.3), rgba(255, 215, 0, 0.2));
    }
  }

  @media (max-height: 800px) {
    font-size: clamp(2rem, 4vw, 2.8rem);
    padding: 0.4rem 1rem;
  }

  @media (max-height: 600px) {
    font-size: clamp(1.5rem, 3vw, 2.2rem);
    padding: 0.3rem 0.8rem;
  }
`;

// Debug info display
const DebugInfo = styled.div`
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-size: 14px;
  z-index: 1000;
  pointer-events: none;
`;

// Loading indicator for video
const LoadingIndicator = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20px 30px;
  border-radius: 10px;
  font-size: 18px;
  z-index: 1001;
  pointer-events: none;
  animation: pulse 2s infinite;
  
  @keyframes pulse {
    0% { opacity: 0.7; }
    50% { opacity: 1; }
    100% { opacity: 0.7; }
  }
`;

// Toggle button for switching between video and photo backgrounds
const ToggleButton = styled(motion.button)`
  position: absolute;
  bottom: 30px;
  left: 30px; /* Moved to right side for better visibility */
  background: linear-gradient(135deg, rgba(255, 154, 158, 1) 0%, rgba(255, 106, 136, 1) 100%);
  color: white;
  border: 3px solid rgba(255, 255, 255, 1);
  border-radius: 50px;
  padding: 12px 24px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  z-index: 1000; /* Increased z-index to ensure it's above everything */
  opacity: 1;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.4), 0 0 30px rgba(255, 255, 255, 0.3);
  animation: ${pulse} 2s infinite alternate;

  &:hover {
    opacity: 1;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.3);
  }

  &:active {
    transform: translateY(1px);
  }
`;

export default PhotoVideoBackground;