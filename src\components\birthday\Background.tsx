import React from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import Particles from 'react-tsparticles';
import { loadFull } from 'tsparticles';
import type { Engine } from 'tsparticles-engine';
import PhotoVideoBackground from './PhotoVideoBackground';

const Background: React.FC = () => {
  const particlesInit = async (engine: Engine) => {
    await loadFull(engine);
  };

  return (
    <Container>
      {/* Photo/Video Background with girlfriend's photos */}
      <PhotoVideoBackground />

      {/* Gradient background with reduced opacity to let photos show through */}
      <GradientBackground
        initial={{ opacity: 0 }}
        animate={{ opacity: 0.4 }}
        transition={{ duration: 2 }}
      />
      {/* Reduced opacity to make video/photos more visible */}

      {/* Particles background */}
      <ParticlesContainer>
        <Particles
          id="tsparticles"
          init={particlesInit}
          options={{
            fullScreen: false,
            background: {
              color: {
                value: "transparent",
              },
            },
            fpsLimit: 120,
            particles: {
              color: {
                value: "#ffffff",
              },
              links: {
                color: "#ffffff",
                distance: 150,
                enable: true,
                opacity: 0.2,
                width: 1,
              },
              move: {
                direction: "none",
                enable: true,
                outModes: {
                  default: "bounce",
                },
                random: true,
                speed: 1,
                straight: false,
              },
              number: {
                density: {
                  enable: true,
                  area: 1200, // Increased area to reduce particle density
                },
                value: 50, // Reduced number of particles
              },
              opacity: {
                value: 0.2, // Reduced opacity to make video/photos more visible
              },
              shape: {
                type: "circle",
              },
              size: {
                value: { min: 1, max: 3 },
              },
            },
            detectRetina: true,
          }}
        />
      </ParticlesContainer>

      {/* Animated stars */}
      <StarsContainer>
        {Array.from({ length: 30 }).map((_, index) => ( // Reduced number of stars
          <Star
            key={index}
            style={{
              top: `${Math.random() * 100}%`,
              left: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 5}s`,
              width: `${2 + Math.random() * 3}px`,
              height: `${2 + Math.random() * 3}px`,
            }}
          />
        ))}
      </StarsContainer>
    </Container>
  );
};

const Container = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 1;
`;

const GradientBackground = styled(motion.div)`
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    #0f0c29 0%,
    #302b63 50%,
    #24243e 100%
  );
  animation: gradientShift 15s ease infinite;
  z-index: 2;
  opacity: 0.5; /* Reduced opacity to let video/photos show through better */

  @keyframes gradientShift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }
`;

const ParticlesContainer = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 3;
`;

const StarsContainer = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 4;
`;

const Star = styled.div`
  position: absolute;
  background-color: white;
  border-radius: 50%;
  animation: twinkle 5s infinite alternate;

  @keyframes twinkle {
    0% {
      opacity: 0.2;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.5);
    }
    100% {
      opacity: 0.2;
      transform: scale(1);
    }
  }
`;

export default Background;
