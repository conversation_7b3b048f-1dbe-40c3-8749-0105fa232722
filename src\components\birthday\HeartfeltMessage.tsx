import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import Typed from 'typed.js';
import { FaHeart } from 'react-icons/fa';

interface HeartfeltMessageProps {
  name: string;
  onContinue: () => void;
}

const HeartfeltMessage: React.FC<HeartfeltMessageProps> = ({ name, onContinue }) => {
  const messageRef = useRef<HTMLDivElement>(null);
  const typedRef = useRef<Typed | null>(null);
  const [isMessageComplete, setIsMessageComplete] = useState(false);
  const [isButtonVisible, setIsButtonVisible] = useState(false);

  // Personalized message with Nepali greeting
  const message = `Dear ${name},

जन्मदिनको धेरै धेरै शुभकामना कान्छी! ❤️

Every moment we shared is precious to me. Though we had our misunderstandings, you hold a special place in my heart. Your innocent smile and sweet nature always brightens my darkest days.

I know relationships need understanding and compromise. I'm here, with open arms and an open heart, hoping we can make this bond stronger. I'm sorry for any hurt I caused.

You mean everything to me. Happy Birthday!

जन्मदिनको धेरै धेरै शुभकामना कान्छी! ❤️`;

  useEffect(() => {
    if (messageRef.current) {
      typedRef.current = new Typed(messageRef.current, {
        strings: [message],
        typeSpeed: 40,
        showCursor: true,
        cursorChar: '|',
        onComplete: () => {
          setIsMessageComplete(true);
          setTimeout(() => {
            setIsButtonVisible(true);
          }, 2000);
        }
      });
    }

    return () => {
      typedRef.current?.destroy();
    };
  }, [message]);

  const handleContinue = () => {
    onContinue();
  };

  return (
    <Container>
      <ContentWrapper
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 1 }}
      >
        <MessageContainer>
          <MessageContent ref={messageRef}></MessageContent>
        </MessageContainer>

        {isMessageComplete && (
          <HintMessage
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1, delay: 1 }}
          >
            {/* Continue to see your final surprise with special hints and gifts! */}
          </HintMessage>
        )}

        {/* Always visible emergency button */}
        <EmergencyButton
          initial={{ opacity: 1 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          onClick={handleContinue}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          CLICK HERE - Go to Grand Finale
        </EmergencyButton>

        {isButtonVisible && (
          <ContinueButton
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            onClick={handleContinue}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Continue to Your Surprise
          </ContinueButton>
        )}
      </ContentWrapper>

      <FloatingHearts>
        {Array.from({ length: 30 }).map((_, index) => (
          <Heart
            key={index}
            style={{
              left: `${Math.random() * 100}%`,
              animationDuration: `${Math.random() * 15 + 10}s`,
              animationDelay: `${Math.random() * 10}s`,
              fontSize: `${Math.random() * 30 + 10}px`,
              opacity: Math.random() * 0.7 + 0.3
            }}
          >
            <FaHeart />
          </Heart>
        ))}
      </FloatingHearts>
    </Container>
  );
};

const Container = styled.div`
  position: relative;
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Changed from center to flex-start */
  background: linear-gradient(135deg, #c471f5 0%, #fa71cd 100%);
  overflow: hidden;
  padding: 2rem 0; /* Add padding to ensure content doesn't touch edges */
  box-sizing: border-box;
`;

const ContentWrapper = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* Changed from center to flex-start */
  width: 90%;
  max-width: 800px;
  z-index: 10;
  min-height: calc(100vh - 4rem); /* Ensure it takes full height minus padding */
  max-height: calc(100vh - 4rem); /* Prevent overflow */
  overflow-y: auto; /* Allow scrolling if content is too long */
  padding-top: 2rem; /* Add some top spacing */
  box-sizing: border-box;
`;

const MessageContainer = styled.div`
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 6rem; /* Increased margin to leave space for fixed button */
  width: 100%;
  max-width: 700px;
  max-height: calc(100vh - 12rem); /* Limit height to prevent overflow */
  overflow-y: auto; /* Allow scrolling within message container if needed */

  @media (max-height: 600px) {
    padding: 1.5rem;
    max-height: calc(100vh - 8rem);
    margin-bottom: 4rem;
  }
`;

const MessageContent = styled.div`
  font-family: 'Dancing Script', cursive, sans-serif;
  font-size: 1.5rem;
  line-height: 1.8;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  white-space: pre-line;

  .typed-cursor {
    opacity: 1;
    animation: typedjsBlink 0.7s infinite;
  }

  @keyframes typedjsBlink {
    50% {
      opacity: 0.0;
    }
  }
`;

const HintMessage = styled(motion.div)`
  font-family: 'Dancing Script', cursive, sans-serif;
  font-size: 1.3rem;
  color: white;
  text-align: center;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.2);
  padding: 1rem 2rem;
  border-radius: 50px;
  max-width: 80%;
`;

const EmergencyButton = styled(motion.button)`
  position: fixed; /* Fixed positioning to stay visible */
  bottom: 2rem; /* Position from bottom */
  left: 50%; /* Center horizontally */
  transform: translateX(-50%); /* Center horizontally */
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #ff9a9e 0%, #ff6a88 100%);
  border: none;
  border-radius: 50px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  z-index: 1000; /* High z-index to stay on top */

  @media (max-height: 600px) {
    bottom: 1rem;
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }
`;

const ContinueButton = styled(motion.button)`
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #ff9a9e 0%, #ff6a88 100%);
  border: none;
  border-radius: 50px;
  color: white;
  font-size: 1.2rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
`;

const FloatingHearts = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  pointer-events: none;
`;

const Heart = styled.div`
  position: absolute;
  bottom: -10%;
  color: rgba(255, 255, 255, 0.8);
  animation: float linear infinite;

  @keyframes float {
    0% {
      transform: translateY(0) rotate(0deg);
    }
    100% {
      transform: translateY(-1000px) rotate(360deg);
    }
  }
`;

export default HeartfeltMessage;
