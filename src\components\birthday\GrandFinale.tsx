import React, { useEffect, useState } from 'react';
import styled, { keyframes } from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import Confetti from 'react-confetti';
import { FaGift, FaHeart, FaRedo } from 'react-icons/fa';

interface GrandFinaleProps {
  name: string;
  onRestart: () => void;
}

const GrandFinale: React.FC<GrandFinaleProps> = ({ name, onRestart }) => {
  const [windowSize, setWindowSize] = useState({
    width: window.innerWidth,
    height: window.innerHeight,
  });

  const [showConfetti, setShowConfetti] = useState(true);
  const [showHints, setShowHints] = useState(false);
  const [showLoveMessage, setShowLoveMessage] = useState(false);
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0);
  const [showAllQuotes, setShowAllQuotes] = useState(false);

  const loveQuotes = [
    "तिमी मेरो जीवनको सबैभन्दा राम्रो उपहार हौ ❤️",
    "म तिमीलाई सधैं माया गर्नेछु 💕",
    "तिमी मेरो हरेक सपनामा छौ 💫",
    "हाम्रो माया सधैं यसरी नै बढ्दै जाओस् 🌹",
    "तिम्रो मुस्कानले मेरो दिन बनाउँछ ☀️",
    "तिमी मेरो सबैभन्दा मिठो सपना हौ 💭",
    "तिम्रो साथमा हरेक दिन खुशी छ 🌈"
  ];

  useEffect(() => {
    const handleResize = () => {
      setWindowSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };

    window.addEventListener('resize', handleResize);

    const timer = setTimeout(() => {
      setShowHints(true);
    }, 3000);

    const messageTimer = setTimeout(() => {
      setShowLoveMessage(true);
    }, 5000);

    const confettiTimer = setTimeout(() => {
      setShowConfetti(false);
    }, 10000);

    // Cycle through love quotes
    const quoteInterval = setInterval(() => {
      if (showLoveMessage && !showAllQuotes) {
        setCurrentQuoteIndex((prev) => (prev + 1) % loveQuotes.length);
      }
    }, 3000);

    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(timer);
      clearTimeout(messageTimer);
      clearTimeout(confettiTimer);
      clearInterval(quoteInterval);
    };
  }, []);

  return (
    <Container>
      {showConfetti && (
        <Confetti
          width={windowSize.width}
          height={windowSize.height}
          recycle={true}
          numberOfPieces={200}
          gravity={0.1}
          colors={['#FF69B4', '#FFB6C1', '#FFC0CB', '#FF1493']}
        />
      )}

      <FloatingHearts>
        {Array.from({ length: 20 }).map((_, index) => (
          <FloatingHeart
            key={index}
            style={{
              left: `${Math.random() * 100}%`,
              animationDuration: `${Math.random() * 3 + 2}s`,
              animationDelay: `${Math.random() * 2}s`,
              fontSize: `${Math.random() * 20 + 15}px`
            }}
          >
            {['❤️', '💖', '💝', '💕', '💗'][Math.floor(Math.random() * 5)]}
          </FloatingHeart>
        ))}
      </FloatingHearts>

      <ContentWrapper>
        <Title
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 20,
            delay: 0.5
          }}
        >
          जन्मदिनको धेरै धेरै शुभकामना कान्छी! ❤️
        </Title>

        <Subtitle
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 1 }}
        >
          म आगो हुँदा बन्नु है पानी, गल्ती भइहाल्छ जाना-नजानी...
        </Subtitle>

        <AnimatedHeart
          initial={{ scale: 0 }}
          animate={{ scale: [0, 1.2, 1] }}
          transition={{ delay: 1, duration: 1 }}
          whileHover={{ scale: 1.2 }}
          whileTap={{ scale: 0.9 }}
        >
          <FaHeart size={80} />
        </AnimatedHeart>

        {showLoveMessage && (
          <LoveQuotesContainer
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            {showAllQuotes ? (
              // Show all quotes
              loveQuotes.map((quote, index) => (
                <LoveQuote
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.3 }}
                >
                  {quote}
                </LoveQuote>
              ))
            ) : (
              // Show single cycling quote with smooth transitions
              <AnimatePresence mode="wait">
                <LoveQuote
                  key={currentQuoteIndex}
                  initial={{ opacity: 0, scale: 0.8, y: 20 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.8, y: -20 }}
                  transition={{ duration: 0.5 }}
                >
                  {loveQuotes[currentQuoteIndex]}
                </LoveQuote>
              </AnimatePresence>
            )}

            <QuoteToggleButton
              onClick={() => setShowAllQuotes(!showAllQuotes)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {showAllQuotes ? 'Show One Quote' : 'Show All Quotes'}
            </QuoteToggleButton>
          </LoveQuotesContainer>
        )}

        {showHints && (
          <HintsContainer
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 1 }}
          >
            <HintTitle>Your Special Surprises Await...</HintTitle>
            <HintsList>
              <HintItem>
                <HintIcon><FaGift /></HintIcon>
                <HintText>A special gift is waiting for you under your pillow</HintText>
              </HintItem>
              <HintItem>
                <HintIcon><FaGift /></HintIcon>
                <HintText>Check your favorite book for a hidden love note</HintText>
              </HintItem>
              <HintItem>
                <HintIcon><FaGift /></HintIcon>
                <HintText>A special message awaits you on your phone</HintText>
              </HintItem>
            </HintsList>
          </HintsContainer>
        )}

        <FinalMessage
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 1 }}
        >
          हाम्रो माया सधैं बलियो र सुन्दर रहोस् ❤️
        </FinalMessage>

        <RestartButton
          onClick={onRestart}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <FaRedo style={{ marginRight: '8px' }} /> Start Over
        </RestartButton>
      </ContentWrapper>
    </Container>
  );
};

const Container = styled.div`
  position: relative;
  width: 100vw;
  min-height: 100vh;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: flex-start; /* Changed from center to flex-start for proper scrolling */
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 50%, #8a2be2 100%);
  overflow-x: hidden;
  overflow-y: auto; /* Enable vertical scrolling */
  padding: 2rem 1rem;
  box-sizing: border-box;

  @media (max-height: 800px) {
    height: 100vh; /* Ensure full height */
    padding: 1rem;
  }

  @media (max-width: 768px) {
    padding: 1rem 0.5rem;
  }

  /* Custom scrollbar styling */
  ::-webkit-scrollbar {
    width: 12px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.4);
    border-radius: 6px;
    border: 2px solid transparent;
    background-clip: content-box;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.6);
    background-clip: content-box;
  }
`;

const ContentWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  text-align: center;
  padding: clamp(1rem, 3vw, 2rem);
  padding-bottom: 3rem; /* Extra bottom padding for better scrolling */
  z-index: 10;
  max-width: min(90vw, 800px);
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin: 2rem auto; /* Always add margin for proper spacing */
  min-height: auto; /* Allow content to determine height */

  @media (max-height: 800px) {
    margin: 1rem auto;
    padding: 1rem;
    padding-bottom: 2rem;
  }

  @media (max-width: 768px) {
    margin: 1rem auto;
    max-width: 95vw;
    padding: 1rem;
  }
`;

const Title = styled(motion.h1)`
  font-size: clamp(2rem, 5vw, 4rem);
  color: white;
  text-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  margin-bottom: 1.5rem;
  text-align: center;
  background: linear-gradient(45deg, #FFD700, #FFA500, #FF69B4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
`;

const Subtitle = styled(motion.p)`
  font-size: 1.5rem;
  color: white;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
`;

const HintsContainer = styled(motion.div)`
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2));
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: clamp(0.8rem, 2vw, 1.5rem);
  margin: 1.5rem 0;
  width: 100%;
  max-width: min(90%, 600px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);

  @media (max-height: 800px) {
    margin: 1rem 0;
    padding: 0.8rem;
  }
`;

const HintTitle = styled.h3`
  font-size: 1.8rem;
  color: white;
  margin-bottom: 1.5rem;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
`;

const HintsList = styled.ul`
  list-style: none;
  padding: 0;
  text-align: left;
`;

const HintItem = styled.li`
  display: flex;
  align-items: center;
  margin-bottom: 1.2rem;
  color: white;
  font-size: 1.2rem;
`;

const HintIcon = styled.span`
  margin-right: 1rem;
  color: #ff4b8d;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  padding: 5px;
`;

const HintText = styled.span`
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const QuoteToggleButton = styled(motion.button)`
  padding: 0.8rem 1.5rem;
  background: linear-gradient(135deg, #ff9a9e 0%, #ff6a88 100%);
  border: none;
  border-radius: 50px;
  color: white;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-top: 1rem;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #ff6a88 0%, #ff9a9e 100%);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
`;

const RestartButton = styled(motion.button)`
  display: flex;
  align-items: center;
  padding: clamp(0.8rem, 2vw, 1rem) clamp(1.5rem, 3vw, 2rem);
  background: linear-gradient(135deg, #ff9a9e 0%, #ff6a88 100%);
  border: none;
  border-radius: 50px;
  color: white;
  font-size: clamp(1rem, 2vw, 1.2rem);
  font-weight: bold;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-top: 2rem;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, #ff6a88 0%, #ff9a9e 100%);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  }
`;

const floatUpAnimation = keyframes`
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100vh) rotate(360deg);
    opacity: 0;
  }
`;

const FloatingHearts = styled.div`
  position: fixed;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
`;

const FloatingHeart = styled.div`
  position: absolute;
  animation: ${floatUpAnimation} linear infinite;
  filter: drop-shadow(0 0 5px rgba(255, 192, 203, 0.5));
`;

const AnimatedHeart = styled(motion.div)`
  color: #ff4b8d;
  margin: 1.5rem 0;
  filter: drop-shadow(0 4px 10px rgba(0, 0, 0, 0.3));
  cursor: pointer;
  transition: filter 0.3s ease;

  &:hover {
    filter: drop-shadow(0 6px 15px rgba(255, 75, 141, 0.5));
  }
`;

const LoveQuotesContainer = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.8rem;
  margin: 1.5rem 0;
  width: 100%;
  max-width: 600px;

  @media (max-height: 800px) {
    gap: 0.5rem;
    margin: 1rem 0;
  }
`;

const LoveQuote = styled(motion.p)`
  font-size: clamp(1rem, 2.5vw, 1.5rem);
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2));
  padding: 1rem 2rem;
  border-radius: 20px;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  text-align: center;
  transform-origin: center;
  transition: transform 0.3s ease;

  &:hover {
    transform: scale(1.05);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.3));
  }
`;

const FinalMessage = styled(motion.div)`
  font-size: clamp(1.2rem, 3vw, 2rem);
  color: white;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  margin: 2rem 0;
  padding: 1rem;
  background: linear-gradient(135deg, #ff6b6b 0%, #4ecdc4 100%);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(5px);
  text-align: center;
  
  @media (max-width: 768px) {
    margin: 1rem 0;
    padding: 0.8rem;
  }
`;

export default GrandFinale;
