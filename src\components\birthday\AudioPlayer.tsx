import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FaMusic, FaVolumeUp, FaVolumeMute } from 'react-icons/fa';

// Using direct path for better compatibility
// This path is relative to the public directory in development
const backgroundMusic = './src/assets/audio/birthday.mp3';

const AudioPlayer: React.FC = () => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [volume, setVolume] = useState(0.5);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    // Hide controls after 10 seconds (increased from 5 for better visibility)
    const timer = setTimeout(() => {
      setShowControls(false);
    }, 10000);

    // Add event listener for user interaction to enable autoplay
    const enableAudio = () => {
      if (audioRef.current) {
        audioRef.current.play().then(() => {
          console.log("Audio started playing");
          setIsPlaying(true);
        }).catch(err => {
          console.error("Audio play error:", err);
        });
      }
      document.removeEventListener('click', enableAudio);
    };

    document.addEventListener('click', enableAudio);

    return () => {
      clearTimeout(timer);
      document.removeEventListener('click', enableAudio);
    };
  }, []);

  // Effect to handle volume changes
  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
    }
  }, [volume]);

  // Effect to handle play/pause state
  useEffect(() => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.play().catch(err => {
          console.error("Error playing audio:", err);
        });
      } else {
        audioRef.current.pause();
      }
    }
  }, [isPlaying]);

  const togglePlay = () => {
    setIsPlaying(!isPlaying);
    setShowControls(true);

    // Reset the auto-hide timer
    setTimeout(() => {
      setShowControls(false);
    }, 10000);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
  };

  return (
    <Container
      initial={{ y: 100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 1, duration: 0.8 }}
      onMouseEnter={() => setShowControls(true)}
    >
      <audio
        ref={audioRef}
        src={backgroundMusic}
        loop
        preload="auto"
        muted={!isPlaying}
        style={{ display: 'none' }}
        onCanPlay={() => console.log("Audio can play now")}
        onError={(e) => console.error("Audio error:", e)}
      />

      <ControlsWrapper>
        <PromptMessage
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: !isPlaying ? 1 : 0, y: !isPlaying ? 0 : 20 }}
          transition={{ duration: 0.5 }}
        >
          Click to play music ↓
        </PromptMessage>

        <ControlsContainer
          initial={{ opacity: 1 }}
          animate={{ opacity: showControls ? 1 : 0 }}
          transition={{ duration: 0.5 }}
        >
          <MusicIcon>
            <FaMusic />
          </MusicIcon>

          <Button onClick={togglePlay}>
            {isPlaying ? (
              <>
                <FaVolumeMute /> Mute
              </>
            ) : (
              <>
                <FaVolumeUp /> Play Music
              </>
            )}
          </Button>

          {isPlaying && (
            <VolumeControl>
              <VolumeSlider
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={volume}
                onChange={handleVolumeChange}
              />
            </VolumeControl>
          )}
        </ControlsContainer>
      </ControlsWrapper>
    </Container>
  );
};

const Container = styled(motion.div)`
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 100;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
`;

const ControlsWrapper = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
`;

const PromptMessage = styled(motion.div)`
  color: white;
  font-size: 14px;
  margin-bottom: 8px;
  background: rgba(0, 0, 0, 0.5);
  padding: 5px 10px;
  border-radius: 15px;
  text-shadow: 0 0 5px rgba(255, 105, 180, 0.7);
  font-weight: bold;
`;

const ControlsContainer = styled(motion.div)`
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  border-radius: 30px;
  padding: 8px 15px;
  margin-top: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
`;

const MusicIcon = styled.div`
  color: #ff9a9e;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite alternate;

  @keyframes pulse {
    0% {
      transform: scale(1);
      opacity: 0.7;
    }
    100% {
      transform: scale(1.2);
      opacity: 1;
    }
  }
`;

const Button = styled.button`
  background: linear-gradient(135deg, #ff9a9e 0%, #ff6a88 100%);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 8px 15px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: bold;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2), 0 0 15px rgba(255, 105, 180, 0.4);
  animation: pulse 2s infinite alternate;

  @keyframes pulse {
    0% {
      transform: scale(1);
    }
    100% {
      transform: scale(1.05);
    }
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(255, 105, 180, 0.6);
    animation: none;
  }
`;

const VolumeControl = styled.div`
  margin-left: 10px;
  display: flex;
  align-items: center;
`;

const VolumeSlider = styled.input`
  -webkit-appearance: none;
  width: 80px;
  height: 4px;
  border-radius: 2px;
  background: rgba(255, 255, 255, 0.3);
  outline: none;

  &::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  }

  &::-moz-range-thumb {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: white;
    cursor: pointer;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
    border: none;
  }
`;



export default AudioPlayer;
