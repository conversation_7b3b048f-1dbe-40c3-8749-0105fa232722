# Adding Video Background

To add a video background to your birthday animation, follow these steps:

1. Place your video file in this directory (`src/assets/videos/`)
2. Name your video file `background.mp4` (or update the path in the `PhotoVideoBackground.tsx` component)
3. For better browser compatibility, consider adding a WebM version named `background.webm`

## Video Requirements

- **Format**: MP4 (H.264) and/or WebM
- **Resolution**: 1080p (1920x1080) or higher recommended
- **Duration**: 30 seconds or longer (it will loop automatically)
- **Content**: Choose a video that complements the birthday theme (e.g., soft particles, gentle animations, romantic scenery)
- **File Size**: Keep it under 10MB if possible for better performance

## Tips

- Use a video with soft, subtle movements for the best effect
- Ensure the video has good contrast with the text overlays
- If your video has audio, it will be muted by default
- The component will automatically fall back to the photo slideshow if the video fails to load
